[package]
name = "shiva"
version = "1.4.9"
edition = "2021"
authors = ["<PERSON><PERSON><PERSON> <i<PERSON><PERSON><PERSON><PERSON>@gmail.com>"]
repository = "https://github.com/igumnoff/shiva"
documentation = "https://docs.rs/shiva"
license = "MIT OR Apache-2.0"
keywords = [
    "parser",
    "conversion",
    "transformer",
    "text-processing",
    "data-conversion",
]
categories = [
    "parser-implementations",
    "text-processing",
    "command-line-utilities",
    "data-structures",
    "data-structures",
]
readme = "../README.md"
description = "Shiva library: Implementation in Rust of a parser and generator for documents of any type"


exclude = ["fonts", "test"]


[package.metadata.docs.rs]
features = ["text", "markdown", "html", "pdf", "json", "xml", "csv", "docx", "xlsx", "xls", "ods"]


[dependencies]
anyhow = "1.0.75"
icu_locid = "1.5.0"
bytes = { version = "1.5.0", features = ["serde"] }
thiserror = "1.0.44"
regex = { version = "1.10.3", optional = true }
scraper = { version = "0.19.0", optional = true }
ego-tree = { version = "0.6.2", optional = true }
lopdf = { version = "0.32.0", optional = true }
serde_json = { version = "1.0.116", optional = true }
serde = { version = "1.0.198", features = ["derive"], optional = true }
serde-xml-rs = { version = "0.6.0", optional = true }
quick-xml = { version = "0.31.0", optional = true }
csv = { version = "1.3.0", optional = true }
typst = { version = "0.11.0", optional = true }
ttf-parser = { version = "0.20.0", optional = true }
comemo = { version = "0.4.0", optional = true }
time = { version = "0.3.36", optional = true }
typst-pdf = { version = "0.11.0", optional = true }
rtf-parser = { version = "0.3.0", optional = true }
docx-rs =  { version = "0.4.17", optional = true }
pulldown-cmark = { version = "0.11.0", optional = true }
calamine = { version = "0.24.0", optional = true }
rust_xlsxwriter = { version = "0.64.2", optional = true }
shiva-spreadsheet-ods = { version = "0.0.2", optional = true }
strum = { version = "0.26", features = ["derive"] }
ehttp = { version = "=0.5.0",optional = true }
wasm-bindgen = "0.2.92"
image = { version = "0.24.9", optional = true }
comrak = { version = "0.28.0", optional = true }
base64 = { version = "0.22.1", optional = true }
log = "0.4.20"

[target.'cfg(target_arch = "wasm32")'.dependencies]
js-sys = "0.3.69"
wasm-bindgen-futures = "0.4.42"
web-sys ={ version = "0.3.4", features = [
  'Headers',
  'Request',
  'RequestInit',
  'RequestMode',
  'Response',
  'Window',
] }

[dev-dependencies]
env_logger = "0.10.0"
anyhow = "1.0.75"

[[example]]
name = "base64_image_example"
required-features = ["json"]

[[example]]
name = "convert_image_to_base64"
required-features = ["json"]

[features]
default = ["all"]
all = ["text", "markdown", "html", "pdf", "json", "xml", "csv", "docx", "rtf", "xlsx", "xls", "ods"]
text = []
csv = ["dep:csv"]
markdown = ["regex", "pulldown-cmark", "comrak"]
html = ["scraper", "ego-tree"]
pdf = ["lopdf", "typst", "ttf-parser", "comemo", "time", "typst-pdf", "ehttp"]
json = ["serde", "serde_json", "base64", "regex"]
xml = ["serde", "serde-xml-rs", "quick-xml"]
rtf = ["rtf-parser", "image"]
docx = ["docx-rs"]
xlsx = ["calamine", "rust_xlsxwriter"]
xls = ["calamine"]
ods = ["calamine", "shiva-spreadsheet-ods"]
