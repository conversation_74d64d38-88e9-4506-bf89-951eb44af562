[package]
name = "cli"
version = "1.0.0"
edition = "2021"

authors = ["<PERSON><PERSON><PERSON> <igumnov<PERSON><EMAIL>>"]
repository = "https://github.com/igumnoff/shiva"
documentation = "https://docs.rs/shiva"
license-file = "../LICENSE"
keywords = ["parser"]
categories = ["parser-implementations"]
readme = "../README.md"
description = "CLI Shiva: Сonverting documents from any format to any"

[[bin]]
name = "shiva"
path = "src/main.rs"

[dependencies]
clap = { version = "4.5.3", features = ["derive"] }
anyhow = "1.0.75"
bytes = "1.5.0"

shiva = { path = "../lib", features = ["json"] }

[features]
default = ["json"]
json = ["shiva/json"]

